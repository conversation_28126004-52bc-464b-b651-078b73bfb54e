# start with our base image (the foundation) - Jessie / php7.2 / apache
FROM php:7.3-apache

# install common packages 
RUN apt-get update && apt-get install -y \
      	git \
      	curl \ 
      	wget \ 
      	zip \
      	unzip\
      	net-tools\
    && docker-php-ext-configure pdo_mysql --with-pdo-mysql=mysqlnd \
    && docker-php-ext-install \
    	pdo_mysql 

# install composer using curl
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/bin/ --filename=composer

#change uid and gid of apache to docker user uid/gid
RUN usermod -u 1000 www-data && groupmod -g 1000 www-data

#copy ssl certificates
COPY docker/ssl/server.crt /etc/apache2/ssl/server.crt
COPY docker/ssl/server.key /etc/apache2/ssl/server.key

# enable hosts
RUN a2ensite 000-default

#enable apache modules
RUN a2enmod rewrite headers ssl
RUN service apache2 restart

# Expose apache.
EXPOSE 80 443

#set directory
WORKDIR /var/www/html

# Run both cron and apache services
CMD apache2-foreground
