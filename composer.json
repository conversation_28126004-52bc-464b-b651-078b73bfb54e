{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^7.3", "bugsnag/bugsnag-laravel": "^2.20", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^6.3", "laravel/framework": "^7.28", "laravel/passport": "^9.3", "laravel/tinker": "^2.0", "laravel/ui": "^2.4", "lcobucci/jwt": "3.3.3", "nyholm/psr7": "^1.3", "sentry/sentry-laravel": "^4.9", "symfony/psr-http-message-bridge": "^2.0"}, "require-dev": {"fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.1", "phpunit/phpunit": "^8.5", "facade/ignition": "^2.0"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": true}}, "minimum-stability": "dev", "prefer-stable": true}