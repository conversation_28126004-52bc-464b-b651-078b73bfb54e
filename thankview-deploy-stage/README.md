# ThankView API Deployment Guide

This document provides detailed information about the deployment process for the ThankView API application. It explains how the deployment works, how to trigger deployments, and where to find relevant configuration files.

## Table of Contents
- [Overview](#overview)
- [Deployment Architecture](#deployment-architecture)
- [GitHub Actions Workflow](#github-actions-workflow)
- [Ansible Playbooks](#ansible-playbooks)
- [How to Deploy](#how-to-deploy)
- [Troubleshooting](#troubleshooting)
- [Configuration Files](#configuration-files)
- [Monitoring and Verifying Deployments](#monitoring-and-verifying-deployments)
- [Security Considerations](#security-considerations)
- [Best Practices](#best-practices)

## Overview

The ThankView API deployment process uses GitHub Actions and Ansible to automate the deployment of code to the API servers. The deployment can be triggered manually through the GitHub Actions UI or automatically when code is pushed to the staging branch (if configured).

The deployment process:

1. Uses self-hosted GitHub Actions runners (with the `api` label)
2. Executes Ansible playbooks to deploy code to the target servers
3. Updates environment variables from AWS Parameter Store
4. Restarts necessary services (PHP-FPM)

## Deployment Architecture

The deployment architecture consists of:

- **GitHub Repository**: evertrue/ThankView-API
- **GitHub Actions Runners**: Self-hosted Linux runners with the `api` label
- **Target Servers**: EC2 instances tagged with `api_host` in AWS
- **Configuration Management**: Ansible playbooks in the `thankview-deploy-stage` directory
- **Secrets Management**: AWS Parameter Store for environment variables and SSH keys

## GitHub Actions Workflow

The deployment is orchestrated by a GitHub Actions workflow defined in `.github/workflows/stage-api-workflow.yml`. This workflow:

1. Checks out the specified branch (defaults to staging)
2. Sets up Python and installs Ansible and dependencies
3. Configures AWS credentials
4. Sets up SSH keys for deployment
5. Runs the Ansible playbook to deploy the code

### Workflow File Location
`.github/workflows/stage-api-workflow.yml`

### Key Components of the Workflow
- **Trigger**: Manual workflow dispatch or push to staging branch (if configured)
- **Inputs**:
  - `environment`: The environment to deploy to (default: stage)
  - `deploy_target`: The server group to deploy to (default: api)
  - `deploy_branch`: The Git branch to deploy (default: staging)
- **Runner**: Self-hosted Linux runners with the api label
- **Steps**: Checkout code, install dependencies, configure credentials, run Ansible playbook

## Ansible Playbooks

The deployment uses several Ansible playbooks:

### API Deployment Playbook

The API-specific deployment is handled by `api-deploy.yml`, which:

- Deploys to servers tagged as `stage_api_host`
- Clones or updates the Git repository
- Updates environment variables from AWS Parameter Store
- Restarts PHP-FPM

**Location**: `thankview-deploy-stage/api-deploy.yml`

### AWS EC2 Dynamic Inventory

The deployment uses AWS EC2 dynamic inventory to discover target servers:

**Location**: `thankview-deploy-stage/aws_ec2.aws_ec2.yml`

This file defines how Ansible discovers EC2 instances and groups them based on tags.

## How to Deploy

### Manual Deployment via GitHub Actions UI

1. Go to the ThankView-API repository on GitHub
2. Click on the "Actions" tab
3. Select the "Deploy ThankView Stage API" workflow
4. Click "Run workflow"
5. Configure the deployment:
   - Environment: Leave as stage (default)
   - Deploy target: Leave as api (default)
   - Deploy branch: Enter the branch name you want to deploy (default is staging)
6. Click "Run workflow" to start the deployment

### Automatic Deployment on Push (If Configured)

If automatic deployment is enabled, pushing code to the staging branch will trigger a deployment automatically.

To enable automatic deployment:

1. Edit `.github/workflows/stage-api-workflow.yml`
2. Uncomment the push trigger section:
   ```yaml
   push:
     branches:
       - staging
     paths:
       - 'thankview-deploy-stage/**'
   ```
3. Commit and push the changes

## Troubleshooting

### Common Issues

**SSH Key Issues**:
- Ensure the `DEPLOY_SSH_PRIVATE_KEY` secret is properly set in GitHub
- Check that the SSH key has access to the repository

**AWS Credential Issues**:
- Verify that `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` secrets are set in GitHub
- Ensure the AWS credentials have appropriate permissions

**Runner Issues**:
- Check that the self-hosted runners are online and properly configured
- Verify that runners have the correct labels (self-hosted, Linux, X64, ec2, api)

**Ansible Playbook Failures**:
- Check the GitHub Actions logs for detailed error messages
- Run the Ansible playbook manually with `-vvv` for verbose output

### Viewing Logs

Deployment logs can be viewed in the GitHub Actions UI:
1. Go to the "Actions" tab in the repository
2. Click on the specific workflow run
3. Expand the job and step details to view logs

## Configuration Files

### GitHub Actions Workflow
- `.github/workflows/stage-api-workflow.yml`

### Ansible Playbooks
- `thankview-deploy-stage/api-deploy.yml`
- `thankview-deploy-stage/aws_ec2.aws_ec2.yml`

### Environment Variables
Environment variables are stored in AWS Parameter Store under:
- `/thankview-stage/thankview-env` - Main environment variables
- `/thankview-stage/thankview-api-env` - API-specific environment variables

### SSH Keys
The deployment SSH key is stored in AWS Parameter Store under:
- `/thankview-stage/api_deploy`

## Deployment Process Flow

The deployment process follows these steps:

1. **Workflow Trigger**:
   - Manual trigger via GitHub Actions UI
   - Automatic trigger on push to staging branch (if configured)

2. **GitHub Actions Workflow Execution**:
   - Workflow runs on self-hosted runner with the api label
   - Checks out the specified branch from the repository
   - Sets up Python and installs Ansible and dependencies
   - Configures AWS credentials and SSH keys

3. **Ansible Playbook Execution**:
   - The workflow calls `ansible-playbook -i aws_ec2.aws_ec2.yml api-deploy.yml`
   - Ansible uses dynamic inventory to discover target servers
   - Targets servers with the `api_host` tag in AWS

4. **Deployment Steps on Target Servers**:
   - Ensures SSH directory exists
   - Retrieves SSH key from AWS Parameter Store
   - Clones the repository if it doesn't exist, or updates it if it does
   - Resets the repository to clean state
   - Pulls the latest code from the specified branch
   - Updates environment variables from AWS Parameter Store
   - Sets APP_ROLE based on instance tags
   - Restarts PHP-FPM

5. **Completion**:
   - Sends notification (if configured)
   - Workflow completes and reports status in GitHub Actions UI

### Deployment Flow Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  GitHub Action  │     │  Ansible        │     │  Target Server  │
│  Workflow       │────▶│  Playbooks      │────▶│  (EC2 Instance) │
└─────────────────┘     └─────────────────┘     └─────────────────┘
       │                        │                        │
       │                        │                        │
       ▼                        ▼                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Trigger        │     │  api-deploy.yml │     │  Clone/Update   │
│  Deployment     │     │                 │     │  Repository     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  Update Env     │
                                               │  Restart PHP    │
                                               └─────────────────┘
```

## Monitoring and Verifying Deployments

### Monitoring Deployment Progress

**GitHub Actions UI**:
- Real-time logs are available in the GitHub Actions UI
- Navigate to Actions > Workflow runs > Select the specific run
- Expand job steps to see detailed logs

**AWS Console**:
- You can monitor EC2 instances in the AWS Console
- Check CloudWatch metrics for CPU, memory, and network usage during deployment

### Verifying Successful Deployment

After a deployment completes, verify it was successful by:

**Check GitHub Actions Status**:
- The workflow should show a green checkmark if successful
- Review any warnings or errors in the logs

**Verify Application Functionality**:
- Test API endpoints to ensure they work as expected
- Check for any error responses

**Check Server Logs**:
- SSH into the server: `ssh ubuntu@<server-ip>`
- Check PHP-FPM logs: `sudo tail -f /var/log/php8.1-fpm.log`
- Check application logs: `tail -f /var/www/ThankView-API/storage/logs/laravel.log`

**Verify Git Commit**:
- SSH into the server
- Navigate to the application directory: `cd /var/www/ThankView-API`
- Check the current commit: `git log -1`
- Verify it matches the expected commit from the deployed branch

## Security Considerations

### Secrets Management

**GitHub Secrets**:
- AWS credentials and SSH keys are stored as GitHub Secrets
- These secrets are encrypted and only exposed during workflow execution
- Never commit secrets to the repository

**AWS Parameter Store**:
- Environment variables are stored in AWS Parameter Store
- SSH keys for deployment are stored in AWS Parameter Store
- All sensitive parameters are encrypted using AWS KMS

### Access Control

**GitHub Repository**:
- Limit who can push to the repository
- Restrict who can trigger workflow runs
- Use branch protection rules for the staging branch

**AWS IAM**:
- Use the principle of least privilege for AWS IAM roles
- Regularly rotate AWS access keys
- Use IAM roles for EC2 instances where possible

### Network Security

**SSH Access**:
- Limit SSH access to the servers using security groups
- Use SSH keys instead of passwords
- Consider using a bastion host for SSH access

## Best Practices

**Testing Before Deployment**:
- Always test changes locally before deploying
- Consider using a staging environment for testing

**Deployment Frequency**:
- Aim for smaller, more frequent deployments
- This reduces risk and makes rollbacks easier

**Monitoring**:
- Set up monitoring for the API
- Configure alerts for API errors and performance issues

**Rollback Plan**:
- Have a plan for rolling back deployments if issues occur
- Document the rollback process

**Documentation**:
- Keep this README up to date
- Document any changes to the deployment process
