# api-deploy.yml - Deployment for API servers
---
- name: Deploy to API servers
  hosts: stage_api_host
  vars:
    app_path: /var/www/ThankView-API
    git_repo: "**************:evertrue/ThankView-API.git"  # Update this to the correct repo
    git_branch: "{{ deploy_branch | default('staging') }}"
    user: "ubuntu"

  tasks:
    - name: Ensure .ssh directory exists
      ansible.builtin.file:
        path: "/home/<USER>/.ssh"
        state: directory
        mode: '0700'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true

    - name: Get SSH key using AWS CLI
      ansible.builtin.shell: |
        aws ssm get-parameter --name "/thankview-stage/api_deploy" --with-decryption --region us-east-1 --query Parameter.Value --output text
      register: ssh_key_cli
      delegate_to: localhost
      run_once: true
      no_log: true

    - name: Save SSH key from CLI to file
      ansible.builtin.copy:
        content: "{{ ssh_key_cli.stdout }}\n"
        dest: "/home/<USER>/.ssh/api_deploy"
        mode: '0600'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true
      no_log: true

    - name: Check if git repo already exists
      ansible.builtin.stat:
        path: "{{ app_path }}/.git"
      register: git_dir

    - name: Configure Git safe directory
      ansible.builtin.shell:
        cmd: git config --global --add safe.directory {{ app_path }}
      become: true
      become_user: "{{ user }}"
      when: git_dir.stat.exists

    - name: Clone git repository if it doesn't exist
      block:
        - name: Ensure app directory exists
          ansible.builtin.file:
            path: "{{ app_path }}"
            state: directory
            mode: '0755'
            owner: "{{ user }}"
            group: "{{ user }}"
          become: true

      #   - name: Clone repository
      #     ansible.builtin.git:
      #       repo: "{{ git_repo }}"
      #       dest: "{{ app_path }}"
      #       key_file: "/home/<USER>/.ssh/api_deploy"
      #       accept_hostkey: yes
      #       ssh_opts: "-o StrictHostKeyChecking=no"
      #     become: true
      #     become_user: "{{ user }}"
      # when: not git_dir.stat.exists

    - name: Reset local changes before pull
      ansible.builtin.shell:
        cmd: |
          git reset --hard HEAD
          git clean -fd
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"
      when: git_dir.stat.exists

    - name: Check if branch exists
      ansible.builtin.shell:
        cmd: git ls-remote --heads {{ git_repo }} {{ git_branch }} | wc -l
      register: branch_exists
      delegate_to: localhost
      changed_when: false

    - name: Set actual branch to use
      set_fact:
        actual_branch: "{% if branch_exists.stdout | trim == '0' %}staging{% else %}{{ git_branch }}{% endif %}"

    - name: Notify if falling back to staging branch
      debug:
        msg: "Branch '{{ git_branch }}' not found in repository. Falling back to 'staging' branch."
      when: branch_exists.stdout | trim == '0'

    - name: Pull latest code if repo exists
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        update: yes
        version: "{{ actual_branch }}" # Using the actual branch or falling back to staging
        key_file: "/home/<USER>/.ssh/api_deploy"
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
      when: git_dir.stat.exists
      become: true
      become_user: "{{ user }}"

    - name: Clear Laravel caches
      ansible.builtin.shell: "php artisan {{ item }}"
      args:
        chdir: "{{ app_path }}"
      loop:
        - "config:clear"
        - "route:clear"
      become: true
      become_user: "{{ user }}"
      ignore_errors: yes

    # New task to fetch and update .env files from AWS Parameter Store
    - name: Overwrite .env files from SSM via lookup
      block:
        - name: Fetch the full .env blob from Parameter Store
          set_fact:
            env_content: "{{ lookup(
              'aws_ssm',
              '/thankview-stage/thankview-env',
              region='us-east-1',
              decrypt=true
              ) + '\n' }}" # Add newline character here

        - name: Fetch the API-specific .env blob from Parameter Store
          set_fact:
            api_env_content: "{{ lookup(
              'aws_ssm',
              '/thankview-stage/thankview-api-env',
              region='us-east-1',
              decrypt=true
              ) + '\n' }}" # Add newline character here

        - name: Define list of .env paths
          set_fact:
            env_file_paths:
              - /var/www/thank-views/.env
              - /var/www/ThankView-API/.env
              - /var/www/ThankView-Envelope-Builder/.env
              - /var/www/thank-views-ca/.env

        - name: Stat parent directories of each .env file
          stat:
            path: "{{ item | dirname }}"
          loop: "{{ env_file_paths }}"
          register: env_stats
          loop_control:
            label: "{{ item }}"
          ignore_errors: yes

        - name: Overwrite existing .env files with the SSM blob
          copy:
            dest: "{{ item.item }}"
            content: "{% if item.item == '/var/www/ThankView-API/.env' %}{{ api_env_content }}{% else %}{{ env_content }}{% endif %}"
            owner: www-data
            group: www-data
            mode: '0644'
          loop: "{{ env_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item }}"
      become: true

    # Task to set APP_ROLE based on instance tags
    - name: Set APP_ROLE based on server type
      become: true
      block:
        - name: Check if APP_ROLE already exists in .env file
          shell: grep -c "^APP_ROLE=" /var/www/ThankView-API/.env || true
          register: app_role_exists
          changed_when: false

        - name: Gather EC2 facts to get instance tags
          ec2_metadata_facts:
          register: ec2_facts

        - name: Get instance ID
          set_fact:
            instance_id: "{{ ec2_facts.ansible_facts.ansible_ec2_instance_id }}"
          when: ec2_facts is defined

        - name: Install Python pip
          apt:
            name: python3-pip
            state: present
            update_cache: yes

        - name: Install required Python modules for AWS operations
          pip:
            name:
              - boto3
              - botocore
            state: present
            executable: pip3
            extra_args: "--system"  # Install system-wide

        - name: Describe EC2 instance to get tags
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_facts.ansible_facts.ansible_ec2_placement_region }}"
          register: instance_info
          vars:
            ansible_python_interpreter: /usr/bin/python3  # Explicitly set Python interpreter
          when: instance_id is defined

        - name: Extract APP_ROLE from tags
          set_fact:
            app_role_value: "{{ item.value }}"
          loop: "{{ instance_info.instances[0].tags | dict2items }}"  # Fixed loop using dict2items
          when:
            - instance_info is defined
            - instance_info.instances is defined
            - instance_info.instances | length > 0
            - item.key == 'APP_ROLE'

        - name: Debug APP_ROLE value from tags
          debug:
            msg: "Found APP_ROLE tag with value: {{ app_role_value }}"
          when: app_role_value is defined and app_role_value != ''

        - name: Set APP_ROLE in .env file from instance tag
          lineinfile:
            path: /var/www/ThankView-API/.env
            regexp: '^APP_ROLE='
            line: "APP_ROLE={{ app_role_value }}"
            state: present
          when:
            - app_role_exists.stdout == "0" or app_role_exists.stdout == "1"
            - app_role_value is defined
            - app_role_value != ''

    - name: Always restart PHP-FPM
      ansible.builtin.service:
        name: php8.1-fpm
        state: restarted
      become: true