<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\AuthClient;
use App\User;

class MoveAuthClientDataToUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'authClient:user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy Auth Client Data to users table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $authClient = AuthClient::get();
        foreach($authClient as $client) {
            $user = User::where('business_id', $client->business_id)->first();
            if (!$user && $client->business_id) {
                $user = User::create(['name' => $client->name, 'api_token' => $client->secret, 'business_id' => $client->business_id]);
            }
        }
    }
}
