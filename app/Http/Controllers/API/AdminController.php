<?php

namespace App\Http\Controllers\API;

use App\AuthClient;
use App\Client;
use App\User;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Response;
use Validator;

class AdminController extends Controller
{
    private $successStatus = 200;
    private $unathorizedStatus = 401;
    private $validationErrorStatus = 428;

    /**
     * login api.
     *
     * @return \Illuminate\Http\Response
     */
    public function createClient(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'business_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], $this->validationErrorStatus);
        }

        $client = AuthClient::where('business_id', $request->input('business_id'))->first();

        if (! $client) {
            $entry = [
                'name' => $request->name,
                'secret' => base64_encode(hash_hmac('sha256', uniqid(), 'secret', true)),
                'redirect' => isset($request->redirect) ? $request->redirect : 'http://localhost',
                'personal_access_client' => 0,
                'password_client' => 1,
                'revoked' => 0,
                'business_id' => $request->business_id,
            ];

            $client = AuthClient::create($entry);
        
        }
        $user = User::where('business_id', $request->input('business_id'))->first();

        if(!$user) {
            $user = User::create(['name' => $request->name, 'api_token' => $client->secret, 'business_id' => $request->business_id]);
        }

        return response()->json(['data' => [
            'client_id' => $client->id,
            'secret' => $client->secret,
        ]]);
    }

    public function getClient(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'business_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], $this->validationErrorStatus);
        }

        $client = AuthClient::where('business_id', $request->input('business_id'))
            ->where('name', $request->input('name'))
            ->first();

        if (! $client) {
            return response()->json(['error' => 'Client not found'], $this->unathorizedStatus);
        }

        return response()->json(['data' => [
            'client_id' => $client->id,
            'secret' => $client->secret,
        ]]);
    }
}
