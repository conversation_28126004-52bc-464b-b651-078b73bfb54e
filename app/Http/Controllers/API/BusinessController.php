<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\DataTransferService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Response;
use Validator;

class BusinessController extends Controller
{
    public $successStatus = 200;
    public $errorStatus = 401;

    protected $dataService = null;
    protected $dataHeaders = [];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(Request $request, DataTransferService $dataService)
    {
        $this->dataService = $dataService;

        $this->dataHeaders = [
            'Api-Gateway-Token' =>  env('API_GATEWAY_TOKEN'),
            'Api-Region' =>  env('API_REGION'),
            'Content-Type' => 'application/json',
        ];
    }

    public function getAuthInfo(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/auth/info';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function getProjects(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
            'excludeType' => $request->excludeType ? $request->excludeType : null,
            'archived' => $request->archived ? $request->archived : null
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/projects';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function getMetrics(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
            'startDate' => $request->sent_start,
            'endDate' => $request->sent_end,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/metrics';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function getLists(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/business/lists';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }
}
