<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\DataTransferService;
use Illuminate\Http\Request;
use Response;
use Validator;

class RecipientController extends Controller
{
    public $successStatus = 200;
    public $errorStatus = 401;

    protected $dataService = null;
    protected $dataHeaders = [];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(Request $request, DataTransferService $dataService)
    {
        $this->dataService = $dataService;

        $this->dataHeaders = [
            'Api-Gateway-Token' =>  env('API_GATEWAY_TOKEN'),
            'Api-Region' =>  env('API_REGION'),
            'Content-Type' => 'application/json',
        ];
    }

    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
            'page' => 'integer',
            'limit' => 'integer',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
            'page' => $request->page,
            'limit' => $request->limit,
            'search' => isset($request->search) ? $request->search : null,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/guest';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
            'recipients' => 'required|array|max:1000',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
            'guests' => $request->recipients,
            'videoId' => $request->video_id ?: null,
            'send' => $request->send && $request->video_id ?: false
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/guest';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function destroy($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/guest/'.$id;

        return $this->dataService->deleteRequest($url, $this->dataHeaders, $params);
    }

    public function getFields(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
            'requireDonorId' => $request->require_donor_id
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/business/recipientFields';
        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function addRecipientToList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'list_id' => 'required',
            'recipients' => 'required|array|max:1000',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }
        $params = [
            'businessId' => $request->client_data->business_id,
            'list_id' => $request->list_id,
            'guests' => $request->recipients,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/guest/list';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }
}
