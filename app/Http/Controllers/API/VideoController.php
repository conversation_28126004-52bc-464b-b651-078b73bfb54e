<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\DataTransferService;
use Illuminate\Http\Request;
use Response;
use Validator;

class VideoController extends Controller
{
    public $successStatus = 200;
    public $errorStatus = 401;

    protected $dataService = null;
    protected $dataHeaders = [];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(Request $request, DataTransferService $dataService)
    {
        $this->dataService = $dataService;

        $this->dataHeaders = [
            'Api-Gateway-Token' =>  env('API_GATEWAY_TOKEN'),
            'Api-Region' =>  env('API_REGION'),
            'Content-Type' => 'application/json',
        ];
    }

    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer',
            'limit' => 'integer',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'page' => $request->page,
            'limit' => $request->limit,
            'search' => isset($request->search) ? $request->search : null,
            'order' => $request->order,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/video';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'description' => 'required',
            'file' => 'required',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $file = $request->file('file');
        if (! $file) {
            return Response::json(['success' => false, 'message' => 'Error reading video file.']);
        }

        $fileName = basename($file->getClientOriginalName());
        $videoUrl = env('THANKVIEW_MEDIA').'/api/upload/video';
        $videoResponse = $this->dataService->postFile($videoUrl, [], $file, $fileName);

        if (! $videoResponse['success']) {
            return Response::json($videoResponse);
        }

        $videoResponse['video']['title'] = $request->title;
        $videoResponse['video']['description'] = $request->description;

        $params = [
            'businessId' => $request->client_data->business_id,
            'video' => $videoResponse['video'],
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/video';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function save(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'video_length' => 'required|numeric',
            'video_path' => 'required',
            'video_thumbs' => 'required|array|min:1|max:10',
            'video_type' => 'required',
            'width' => 'required|integer',
            'height' => 'required|integer',
            'recipient_ids' => 'array|max:1000',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'title' => $request->title,
            'video_length' => $request->video_length,
            'video_path' => $request->video_path,
            'video_thumbs' => $request->video_thumbs,
            'video_type' => $request->video_type,
            'width' => $request->width,
            'height' => $request->height,
            'projectId' => $request->project_id,
            'recipientIds' => $request->recipient_ids,
            'send' => $request->send ?: false
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/video/save';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function attach(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'video_id' => 'required',
            'project_id' => 'required',
            'recipient_ids' => 'required|array|max:1000',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'videoId' => $request->video_id,
            'projectId' => $request->project_id,
            'recipientIds' => $request->recipient_ids,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/video/attach';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function destroy($id, Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/video/'.$id;

        return $this->dataService->deleteRequest($url, $this->dataHeaders, $params);
    }

    public function reply(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/video/reply';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);

    }

    public function watched(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
        ];
        
        $url = env('THANKVIEW_HOST').'/api-v1/video/watched';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }
}
