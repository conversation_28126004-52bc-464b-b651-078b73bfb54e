<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\DataTransferService;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    protected $dataService = null;
    protected $dataHeaders = [];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(Request $request, DataTransferService $dataService)
    {
        $this->dataService = $dataService;

        $this->dataHeaders = [
            'Api-Gateway-Token' =>  env('API_GATEWAY_TOKEN'),
            'Api-Region' =>  env('API_REGION'),
            'Content-Type' => 'application/json',
        ];
    }

    public function subscribeHook(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
            'url' => $request->hookUrl,
            'type' => $request->hookType,
            'vendor' => $request->vendor,
            'metadata' => json_decode($request->getContent(), true),
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/subscribe/hook';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function unsubscribeHook(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
            'hookId' => $request->hookId,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/unsubscribe/hook';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

}
