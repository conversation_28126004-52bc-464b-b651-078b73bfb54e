<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\DataTransferService;
use Illuminate\Http\Request;
use Response;
use Validator;

class SendController extends Controller
{
    public $successStatus = 200;
    public $errorStatus = 401;

    protected $dataService = null;
    protected $dataHeaders = [];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(Request $request, DataTransferService $dataService)
    {
        $this->dataService = $dataService;

        $this->dataHeaders = [
            'Api-Gateway-Token' =>  env('API_GATEWAY_TOKEN'),
            'Api-Region' =>  env('API_REGION'),
            'Content-Type' => 'application/json',
        ];
    }

    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/send';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function getReadyToSend(Request $request)
    {
        $url = env('THANKVIEW_HOST').'/api-v1/send/readyToSend';

        return $this->getSendData($url, $request);
    }

    public function getScheduled(Request $request)
    {
        $url = env('THANKVIEW_HOST').'/api-v1/send/scheduled';

        return $this->getSendData($url, $request);
    }

    public function getSending(Request $request)
    {
        $url = env('THANKVIEW_HOST').'/api-v1/send/sending';

        return $this->getSendData($url, $request);
    }

    public function getDelivered(Request $request)
    {
        $url = env('THANKVIEW_HOST').'/api-v1/send/delivered';

        return $this->getSendData($url, $request);
    }

    public function getBounced(Request $request)
    {
        $url = env('THANKVIEW_HOST').'/api-v1/send/bounced';

        return $this->getSendData($url, $request);
    }

    protected function getSendData($url, $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
            'page' => 'integer',
            'limit' => 'integer',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
            'page' => $request->page,
            'limit' => $request->limit,
        ];

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function getAllSends(Request $request)
    {
        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id ?: null,
        ];
        
        $url = env('THANKVIEW_HOST').'/api-v1/send/all';

        return $this->dataService->getRequest($url, $this->dataHeaders, $params);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
            'recipient_ids' => 'required|array|max:1000',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
            'recipientIds' => $request->recipient_ids,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/send';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function schedule(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
            'schedule' => 'required|date_format:Y-m-d H:i:s',
            'recipient_ids' => 'required|array|max:1000',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
            'schedule' => $request->schedule,
            'recipientIds' => $request->recipient_ids,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/send';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }

    public function cancel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required',
            'recipient_ids' => 'required|array|max:1000',
        ]);

        if ($validator->fails()) {
            return Response::json(['success' => false, 'data' => ['errors' => $validator->errors()->all()]]);
        }

        $params = [
            'businessId' => $request->client_data->business_id,
            'projectId' => $request->project_id,
            'recipientIds' => $request->recipient_ids,
        ];

        $url = env('THANKVIEW_HOST').'/api-v1/send/cancel';

        return $this->dataService->postRequest($url, $this->dataHeaders, $params);
    }
}
