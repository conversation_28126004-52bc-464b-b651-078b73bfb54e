<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\AuthenticationException;
use <PERSON><PERSON>\Diactoros\ResponseFactory;
use <PERSON><PERSON>\Diactoros\ServerRequestFactory;
use <PERSON><PERSON>\Diactoros\StreamFactory;
use <PERSON><PERSON>\Diactoros\UploadedFileFactory;
use <PERSON><PERSON>\Passport\Http\Middleware\CheckClientCredentials;
use Lara<PERSON>\Passport\TokenRepository;
use League\OAuth2\Server\Exception\OAuthServerException;
use League\OAuth2\Server\ResourceServer;
use Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory;

use App\AuthClient;

class CheckClientAdmin extends CheckClientCredentials
{
    /**
     * The Resource Server instance.
     *
     * @var \League\OAuth2\Server\ResourceServer
     */
    protected $server;

    /**
     * Create a new middleware instance.
     *
     * @param  \League\OAuth2\Server\ResourceServer  $server
     * @return void
     */
    public function __construct(ResourceServer $server)
    {
        $this->server = $server;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$scopes
     * @return mixed
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$scopes)
    {
        $psr = (new PsrHttpFactory(
            new ServerRequestFactory,
            new StreamFactory,
            new UploadedFileFactory,
            new ResponseFactory
        ))->createRequest($request);

        try {
            $psr = $this->server->validateAuthenticatedRequest($psr);
        } catch (OAuthServerException $e) {
            throw new AuthenticationException;
        }

        $this->validateAdmin($psr);

        return $next($request);
    }

    public function validateAdmin($psr)
    {
        $clientId = $psr->getAttribute('oauth_client_id');

        $isAdmin = AuthClient::find($clientId)->is_admin;

        if (! $isAdmin) {
            throw new Exception('Client is not admin');
        }
    }
}
