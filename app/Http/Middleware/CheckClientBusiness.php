<?php

namespace App\Http\Middleware;

use Closure;
use Exception;

use Illuminate\Auth\AuthenticationException;
use <PERSON><PERSON>\Diactoros\ResponseFactory;
use <PERSON><PERSON>\Diactoros\ServerRequestFactory;
use <PERSON><PERSON>\Diactoros\StreamFactory;
use <PERSON><PERSON>\Diactoros\UploadedFileFactory;
use <PERSON><PERSON>\Passport\TokenRepository;
use League\OAuth2\Server\Exception\OAuthServerException;
use League\OAuth2\Server\ResourceServer;
use Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory;
use Laravel\Passport\Exceptions\MissingScopeException;
use <PERSON><PERSON>\Passport\Http\Middleware\CheckClientCredentials;
use Illuminate\Contracts\Auth\Factory as Auth;

use App\AuthClient;

class CheckClientBusiness extends CheckClientCredentials
{
    /**
     * The Resource Server instance.
     *
     * @var \League\OAuth2\Server\ResourceServer
     */
    protected $server;

    /**
     * Token Repository.
     *
     * @var \Laravel\Passport\TokenRepository
     */
    protected $repository;

    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @param  \League\OAuth2\Server\ResourceServer  $server
     * @return void
     */
    public function __construct(ResourceServer $server, TokenRepository $repository, Auth $auth)
    {
        $this->server = $server;
        $this->repository = $repository;
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$scopes
     * @return mixed
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$scopes)
    {
        $psr = (new PsrHttpFactory(
            new ServerRequestFactory,
            new StreamFactory,
            new UploadedFileFactory,
            new ResponseFactory
        ))->createRequest($request);

        try {
            $psr = $this->server->validateAuthenticatedRequest($psr);
            $this->getValidateBusiness($psr, $request);
        } catch (OAuthServerException $e) {
            if(auth('api')->user()) {
                $request['client_data'] = auth('api')->user();
            } else {
                throw new AuthenticationException;
            }
        }

        /** does't work, look into this later */
        // if (empty($guards)) {
        //     $guards = [null];
        // }

        // foreach ($guards as $guard) {
        //     if ($this->auth->guard($guard)->check()) {
        //         return $this->auth->shouldUse($guard);
        //     }
        // }

        // $this->validate($psr, $scopes);

        return $next($request);
    }

    public function getValidateBusiness($psr, $request)
    {
        $clientId = $psr->getAttribute('oauth_client_id');

        $client = AuthClient::find($clientId);

        if (! $client->business_id) {
            throw new Exception('Client is not a valid thankview business');
        }

        $request['client_data'] = $client;
    }
}
