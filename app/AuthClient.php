<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Passport\HasApiTokens;

class AuthClient extends Model
{
    protected $table = 'oauth_clients';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    //clients are password client
    protected $fillable = [
        'id', 'user_id', 'name', 'secret', 'redirect', 'personal_access_client', 'password_client', 'revoked', 'business_id', 'is_admin',
    ];
}
