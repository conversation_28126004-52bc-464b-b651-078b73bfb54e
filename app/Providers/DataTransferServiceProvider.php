<?php

namespace App\Providers;

use App\Services\DataTransferService;
use Illuminate\Support\ServiceProvider;

class DataTransferServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->app->bind('DataTransferService', function () {
            return new DataTransferService();
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        return ['DataTransferService'];
    }
}
