<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Psr7\Request;
use Response;

class DataTransferService
{
    protected $client;

    public function __construct()
    {
        if (env('APP_ENV') === 'local') {
            $this->client = new Client(['verify' => false]);
        } else {
            $this->client = new Client();
        }
    }

    public function getRequest($url, $headers = [], $params = [])
    {
        $client = $this->client;

        try {
            $response = $client->request('GET', $url, [
                'headers' => $headers,
                'query' => $params,
                'verify' => false,
            ]);

            $contents = json_decode($response->getBody()->getContents(), true);

            return Response::json(['success' => $contents['success'], 'data' => $contents['data']]);
        } catch (ClientException $e) {
            return Response::json(['success' => false, 'status' => $e->getCode()]);
        }
    }

    public function postRequest($url, $headers = [], $params = [])
    {
        $client = $this->client;

        try {
            $response = $client->request('POST', $url, [
                'headers' => $headers,
                'json' => $params,
            ]);

            $contents = json_decode($response->getBody()->getContents(), true);

            return Response::json(['success' => $contents['success'], 'data' => $contents['data']]);
        } catch (ClientException $e) {
            return Response::json(['success' => false, 'status' => $e->getCode()]);
        }
    }

    public function postFile($url, $headers, $file, $fileName)
    {
        $client = $this->client;

        try {
            $response = $client->request('POST', $url, [
                'headers' => $headers,
                'multipart' => [
                    [
                        'name' => 'file',
                        'contents' => file_get_contents($file),
                        'filename' => $fileName,
                    ],
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (ClientException $e) {
            return ['success' => false];
        }
    }

    public function putRequest($url, $headers = [], $params = [])
    {
        $client = $this->client;

        try {
            $response = $client->request('PUT', $url, [
                'headers' => $headers,
                'json' => $params,
            ]);

            $contents = json_decode($response->getBody()->getContents(), true);

            return Response::json(['success' => $contents['success'], 'data' => $contents['data']]);
        } catch (ClientException $e) {
            return Response::json(['success' => false, 'status' => $e->getCode()]);
        }
    }

    public function deleteRequest($url, $headers = [], $params = [])
    {
        $client = $this->client;

        try {
            $response = $client->request('DELETE', $url, [
                'headers' => $headers,
                'json' => $params,
            ]);

            $contents = json_decode($response->getBody()->getContents(), true);

            return Response::json(['success' => $contents['success'], 'data' => $contents['data']]);
        } catch (ClientException $e) {
            return Response::json(['success' => false, 'status' => $e->getCode()]);
        }
    }
}
