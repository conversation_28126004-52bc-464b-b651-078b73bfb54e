version: '3'
# define all services
services:
  # our service is called laravel
  laravel:
    # we want to use the image which is build from our Dockerfile
    build:
      context: .
    # apache is running on port 80 but we want to expose this to port 8080 on our local machine
    ports:
      - 803:80
      - 4433:443
    # we depending on the mysql backend
    volumes:
      - ./:/var/www/html
      - ./docker/httpd-vhosts.conf:/etc/apache2/sites-available/000-default.conf
    container_name: api-thankview.com
  # we use this service to easily view our database on localhost:8081
  phpmyadmin:
    restart: always
    image: phpmyadmin/phpmyadmin
    ports:
      - 8083:80
    environment:
      - PMA_HOST=mysql_api
      - PMA_USER=root
      - PMA_PASSWORD=root
    depends_on:
      - mysql_api
  mysql_api:
    # we use the mysql base image, version 5.7.22
    image: mysql:5.7.26
    # we mount a datavolume to make sure we don't loose data
    ports:
      - 33063:3306
    volumes:
       - db_data:/var/lib/mysql
       - ./docker/init_db:/docker-entrypoint-initdb.d
    # setting some envvars to create the DB
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=thankviews_api
      - MYSQL_ALLOW_EMPTY_PASSWORD=yes
    platform: linux/amd64
    container_name: mysql_api
volumes:
  db_data:
  data:
    driver: local

networks:
  default:
    external:
      name: thankview-app_net
