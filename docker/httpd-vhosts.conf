<VirtualHost *:80>
    ServerName api-thankview.com
    ServerAlias api-thankview.com
    Redirect permanent / https://api-thankview.com:4433/
</VirtualHost>

<VirtualHost *:443>
    ServerName api-thankview.com
    ServerAdmin <EMAIL>
    DocumentRoot /var/www/html/public
    SSLEngine on

    SSLCertificateFile /etc/apache2/ssl/server.crt
    SSLCertificateKeyFile /etc/apache2/ssl/server.key

    <Directory "/var/www/html/public">
          Options Indexes FollowSymLinks MultiViews
          AllowOverride All
          Order allow,deny
          allow from all
    </Directory>
</VirtualHost>