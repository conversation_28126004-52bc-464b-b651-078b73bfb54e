APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:Mbt+H9eJkSngYxwxmtHzk+GqRvrvUOoXdDWL/Um7BJw=
APP_DEBUG=true
APP_URL=https://api-thankview.com
APP_HOST=api-thankview.com

LOG_CHANNEL=stack

DB_CONNECTION=mysql_api
DB_HOST=host.docker.internal
DB_PORT=3306
DB_DATABASE=thankviews_api
DB_USERNAME=root
DB_PASSWORD=root
DB_STRICT_MODE=false

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
SESSION_LIFETIME=120
QUEUE_CONNECTION=sync

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

BUGSNAG_API_KEY=8b72631412023ec90a27cf1ce56dab20

THANKVIEW_HOST=https://dev-thankview.com
THANKVIEW_MEDIA=https://dev-thankview.com

API_GATEWAY_TOKEN=somekey
SENTRY_LARAVEL_DSN=https://<EMAIL>/4508202568777728
SENTRY_TRACES_SAMPLE_RATE=0.1
