<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('/debug-sentry', function () {
    \Sentry\captureMessage('Hello from Sentry!');
    throw new \Exception('Test Sentry error');
});


Route::group(['middleware' => ['admin', 'throttle:240,1']], function () {
    Route::post('/register', 'API\AdminController@createClient');
    Route::post('/client', 'API\AdminController@getClient');
});

Route::group(['middleware' => ['client', 'throttle:240,1']], function () {
    Route::get('/auth', 'API\BusinessController@getAuthInfo');
    Route::get('/projects', 'API\BusinessController@getProjects');
    Route::get('/metrics', 'API\BusinessController@getMetrics');
    Route::get('/lists', 'API\BusinessController@getLists');

    Route::get('/recipient', 'API\RecipientController@index');
    Route::post('/recipient', 'API\RecipientController@store');
    Route::delete('/recipient/{id}', 'API\RecipientController@destroy');
    Route::post('/recipient/fields', 'API\RecipientController@getFields');
    Route::post('/recipient/list', 'API\RecipientController@addRecipientToList');

    Route::get('/video', 'API\VideoController@index');
    Route::get('/video/reply', 'API\VideoController@reply');
    Route::post('/video', 'API\VideoController@store');
    Route::post('/video/save', 'API\VideoController@save');
    Route::post('/video/attach', 'API\VideoController@attach');
    Route::delete('/video/{id}', 'API\VideoController@destroy');
    Route::get('/video/watched', 'API\VideoController@watched');

    Route::get('/send', 'API\SendController@index');
    Route::get('/send/readyToSend', 'API\SendController@getReadyToSend');
    Route::get('/send/scheduled', 'API\SendController@getScheduled');
    Route::get('/send/sending', 'API\SendController@getSending');
    Route::get('/send/delivered', 'API\SendController@getDelivered');
    Route::get('/send/bounced', 'API\SendController@getBounced');
    Route::post('/send/now', 'API\SendController@store');
    Route::post('/send/schedule', 'API\SendController@schedule');
    Route::post('/send/cancelScheduled', 'API\SendController@cancel');
    Route::get('/send/all', 'API\SendController@getAllSends');

    Route::post('/subscribe', 'API\SubscriptionController@subscribeHook');
    Route::post('/unsubscribe', 'API\SubscriptionController@unsubscribeHook');
});
