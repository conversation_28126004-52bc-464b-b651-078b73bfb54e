@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Login') }}</div>

                <div class="card-body">
                    <form method="POST" action='http://dev.api.com/oauth/token' aria-label="{{ __('Token') }}">
                        @csrf

                        <div class="form-group row">
                            <label for="username" class="col-sm-4 col-form-label text-md-right">{{ __('E-Mail Address') }}</label>

                            <div class="col-md-6">
                                <input id="username" type="email" class="form-control{{ $errors->has('username') ? ' is-invalid' : '' }}" name="username" value="{{ old('username') }}" required autofocus>

                                @if ($errors->has('email'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('email') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="password" class="col-md-4 col-form-label text-md-right">{{ __('Password') }}</label>

                            <div class="col-md-6">
                                <input id="password" type="password" class="form-control{{ $errors->has('password') ? ' is-invalid' : '' }}" name="password" required>

                                @if ($errors->has('password'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('password') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                         <div class="form-group row">
                            <label for="client_id" class="col-sm-4 col-form-label text-md-right">{{ __('Client_id') }}</label>

                            <div class="col-md-6">
                                <input id="client_id" type="number" class="form-control{{ $errors->has('client_id') ? ' is-invalid' : '' }}" name="client_id" value="{{ old('client_id') }}" required autofocus>

                                @if ($errors->has('client_id'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('client_id') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="client_secret" class="col-sm-4 col-form-label text-md-right">{{ __('Client_secret') }}</label>

                            <div class="col-md-6">
                                <input id="client_secret" type="text" class="form-control{{ $errors->has('client_secret') ? ' is-invalid' : '' }}" name="client_secret" value="{{ old('client_secret') }}" required autofocus>

                                @if ($errors->has('client_id'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('client_secret') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="grant_type" class="col-sm-4 col-form-label text-md-right">{{ __('grant_type') }}</label>

                            <div class="col-md-6">
                                <input id="grant_type" type="text" class="form-control{{ $errors->has('grant_type') ? ' is-invalid' : '' }}" name="grant_type" value="{{ old('grant_type') }}" required autofocus>

                                @if ($errors->has('grant_type'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('grant_type') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row mb-0">
                            <div class="col-md-8 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Login') }}
                                </button>

                                <a class="btn btn-link" href="{{ route('password.request') }}">
                                    {{ __('Forgot Your Password?') }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
