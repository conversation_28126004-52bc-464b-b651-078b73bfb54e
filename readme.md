## Local Enviornment Setup 

* Edit your /etc/hosts file
	* Add `127.0.0.1 api-thankview.com`

## Build Docker Image 

* In the root directory of the project run the command `dc build` 

* You can see an image has been built using the command `docker images` 

* Docker runs the `Dockerfile` on the root directory of your application to build the image. The docker file contains a series of instructions to install php, apache, and set permissions in order to run the application in docker. 

## Instantiate a Docker Container

* Now run the command `dc up -d` This will build the container which is an instance of your application running. 

* You can see which containers are running with the command `docker ps` 

## Setup Application State
* Install composer packages
	* `dcl composer update`

* Set up your local .env
	* Copy `.env.example` and rename it to `.env`

* You can see the application running on (https://api-thankview.com:4433)

* Create Database table
	* Run your migration files by using the command `dcla migrate` 
    * NOTE: If you get the error "unknown database 'thankviews_api'", you can fix it by running `create database thankviews_api` wherever you normally run sql queries.
	* You can run other artisan commands in similar ways 

* Set up Laravel Passport
	* Setup passport api keys by using the command `dcla passport:install` 

* Set up a client to communicate with ThankView
	* In terminal, run `dcla tinker` from root
	* Create the new client by running `App\AuthClient::create(['name' => 'ThankView', 'secret' => base64_encode(hash_hmac('sha256', uniqid(), 'secret', true)), 'redirect' => 'http://localhost', 'personal_access_client' => 0, 'password_client' => 1, 'revoked' => 0, 'is_admin' => true]);`
	* We will need the "id" and the "secret" for the below instructions

* Update ThankView application environment variables
	* Open the main ThankView application repo
	* Open the .env file
	* Add `THANKVIEW_API_HOST` variable and set it to the domain of this API Gateway. e.g. `https://api-thankview.com`
	* Add `THANKVIEW_API_CLIENT` variable and set it to the id of the client you created above. e.g. `3`
	* Add `THANKVIEW_API_SECRET` variable and set it to the secret of the client you created above.
	* Add `API_GATEWAY_TOKEN` variable and set it any string

* Update environment variables
	* Open the ThankView-API .env file
	* Add `THANKVIEW_HOST` variable and set it to the domain of ThankView. e.g. `https://dev-thankview.com`
	* Add `API_GATEWAY_TOKEN` variable and set it to the same value in the ThankView app

* Enable the API integration in ThankView
	* Log into your ThankView portal
	* Browse to Settings -> Integrations
	* Scroll down and enable "API Access"
	* You should get a Client ID and a Client Secret
	* Open up Postman and test the API by following this guide - https://www.notion.so/thankview/ThankView-API-Testing-Gateway-36ad2a62b2824f4386033fdd343d0abe?pvs=4

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
